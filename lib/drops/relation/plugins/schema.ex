defmodule Drops.Relation.Plugins.Schema do
  @moduledoc """
  Plugin that provides schema definition and automatic inference capabilities.

  This plugin adds the `schema/1` and `schema/2` macros for defining relation schemas.
  It supports both automatic schema inference from database tables and manual schema
  definition with Ecto.Schema syntax.

  ## Schema Macro

  The `schema` macro is the primary way to define relation schemas. It supports several forms:

  ### `schema(table_name)`

  Defines a schema with automatic inference from the database table.

      schema("users")

  ### `schema(table_name, opts)`

  Defines a schema with options. Common options include:

  - `infer: true` - Automatically infer schema from database (default: true)
  - `struct: "CustomName"` - Use custom struct module name instead of default

      schema("users", infer: true)
      schema("users", struct: "Person")

  ### `schema(table_name, opts, do: block)`

  Defines a schema with a manual definition block using Ecto.Schema syntax.

      schema("users") do
        field(:name, :string)
        field(:email, :string)
        field(:active, :boolean, default: true)
        timestamps()
      end

  ### `schema(table_name, opts) do ... end`

  Combines automatic inference with manual customizations.

      schema("users", infer: true) do
        field(:full_name, :string, virtual: true)
        has_many(:posts, MyApp.Posts)
      end

  ## Examples

  ### Basic Automatic Schema Inference

      iex> defmodule DocTest.BasicUsers do
      ...>   use Drops.Relation, repo: MyApp.Repo
      ...>   schema("users")
      ...> end
      iex> schema = DocTest.BasicUsers.schema()
      iex> schema.source
      :users
      iex> is_list(schema.fields)
      true

  ### Accessing Schema Fields

      iex> defmodule DocTest.FieldAccess do
      ...>   use Drops.Relation, repo: MyApp.Repo
      ...>   schema("users")
      ...> end
      iex> schema = DocTest.FieldAccess.schema()
      iex> email_field = schema[:email]
      iex> email_field.name
      :email
      iex> email_field.type
      :string

  ### Manual Schema Definition

      iex> defmodule DocTest.ManualUsers do
      ...>   use Drops.Relation, repo: MyApp.Repo
      ...>   schema("users") do
      ...>     field(:name, :string)
      ...>     field(:email, :string)
      ...>     field(:active, :boolean, default: true)
      ...>   end
      ...> end
      iex> schema = DocTest.ManualUsers.schema()
      iex> field_names = Enum.map(schema.fields, & &1.name)
      iex> :name in field_names
      true
      iex> :email in field_names
      true
      iex> :active in field_names
      true

  ### Custom Struct Name

      iex> defmodule DocTest.CustomStruct do
      ...>   use Drops.Relation, repo: MyApp.Repo
      ...>   schema("users", struct: "Person")
      ...> end
      iex> schema_module = DocTest.CustomStruct.__schema_module__()
      iex> to_string(schema_module) =~ "Person"
      true

  ### Hybrid Approach - Inference with Custom Fields

      iex> defmodule DocTest.HybridUsers do
      ...>   use Drops.Relation, repo: MyApp.Repo
      ...>   schema("users", infer: true) do
      ...>     field(:full_name, :string, virtual: true)
      ...>   end
      ...> end
      iex> schema = DocTest.HybridUsers.schema()
      iex> field_names = Enum.map(schema.fields, & &1.name)
      iex> :email in field_names  # inferred field
      true
      iex> length(field_names) > 5  # has both inferred and custom fields
      true

  ### Struct Generation

      iex> defmodule DocTest.StructGen do
      ...>   use Drops.Relation, repo: MyApp.Repo
      ...>   schema("users")
      ...> end
      iex> user = DocTest.StructGen.struct(%{name: "John", email: "<EMAIL>"})
      iex> user.name
      "John"
      iex> user.email
      "<EMAIL>"
      iex> is_nil(user.id)
      true

  ### Schema Module Access

      iex> defmodule DocTest.ModuleAccess do
      ...>   use Drops.Relation, repo: MyApp.Repo
      ...>   schema("users")
      ...> end
      iex> schema_module = DocTest.ModuleAccess.__schema_module__()
      iex> is_atom(schema_module)
      true
      iex> DocTest.ModuleAccess.__schema__(:source)
      "users"
      iex> :id in DocTest.ModuleAccess.__schema__(:fields)
      true

  ## Field Types and Metadata

  Schema fields contain both Ecto types and original database metadata:

      iex> defmodule DocTest.FieldTypes do
      ...>   use Drops.Relation, repo: MyApp.Repo
      ...>   schema("users")
      ...> end
      iex> schema = DocTest.FieldTypes.schema()
      iex> name_field = schema[:name]
      iex> name_field.type
      :string
      iex> is_map(name_field.meta)
      true

  ## Options

  - `infer: true` - Automatically infer schema from database (default: true)
  - `struct: "CustomName"` - Use custom struct module name instead of default
  - Standard Ecto.Schema options are supported in manual field definitions
  """

  alias Drops.Relation.Schema
  alias Drops.Relation.Schema.Field
  alias Drops.Relation.Cache
  alias Drops.Relation.Generator

  use Drops.Relation.Plugin, imports: [schema: 1, schema: 2, schema: 3]

  defmodule Macros.Schema do
    @moduledoc false

    use Drops.Relation.Plugin.MacroStruct,
      key: :schema,
      struct: [:name, block: nil, fields: nil, opts: [], infer: true]

    def new(name) when is_binary(name) do
      %Macros.Schema{name: name}
    end

    def new(fields, opts) when is_list(fields) do
      %Macros.Schema{name: nil, fields: fields, opts: opts}
    end

    def new(name, opts) when is_binary(name) and is_list(opts) do
      opts = Keyword.delete(opts, :do)
      infer = Keyword.get(opts, :infer, true)

      %{new(name) | opts: opts, infer: infer}
    end

    def new(name, opts, block) when is_binary(name) and is_list(opts) do
      %{new(name, opts) | block: block}
    end
  end

  @doc """
  Defines a schema for the relation.

  This macro supports multiple forms for different use cases:

  ## Forms

  ### `schema(table_name)`

  Defines a schema with automatic inference from the database table.

  - `table_name` - String name of the database table

  ### `schema(table_name, opts)`

  Defines a schema with options.

  - `table_name` - String name of the database table
  - `opts` - Keyword list of options:
    - `infer: true` - Automatically infer schema from database (default: true)
    - `struct: "CustomName"` - Use custom struct module name

  ### `schema(table_name, opts) do ... end`

  Defines a schema with a manual definition block using Ecto.Schema syntax.

  - `table_name` - String name of the database table
  - `opts` - Keyword list of options (same as above)
  - `block` - Schema definition block with field/3, embeds_one/3, etc.

  ### `schema(fields, opts)`

  Creates a projected schema with only the specified fields.

  - `fields` - List of field names (atoms) to include
  - `opts` - Keyword list of options

  ## Returns

  The macro sets up the relation to generate:
  - A `schema/0` function that returns the complete schema metadata
  - A `schema/1` function that returns a specific field by name
  - A `struct/1` function for creating struct instances
  - A `__schema_module__/0` function that returns the generated Ecto schema module

  ## Examples

      # Basic inference
      schema("users")

      # With options
      schema("users", infer: true, struct: "Person")

      # Manual definition
      schema("users") do
        field(:name, :string)
        field(:email, :string)
        timestamps()
      end

      # Hybrid approach
      schema("users", infer: true) do
        field(:full_name, :string, virtual: true)
      end

      # Field projection
      schema([:id, :name, :email], [])
  """
  defmacro schema(fields, opts \\ [])

  defmacro schema(name, opts) when is_binary(name) do
    block = opts[:do]

    quote do
      @context update_context(__MODULE__, Macros.Schema, [
                 unquote(name),
                 unquote(Keyword.delete(opts, :do)),
                 unquote(Macro.escape(block))
               ])
    end
  end

  defmacro schema(fields, opts) when is_list(fields) do
    quote do
      @context update_context(__MODULE__, Macros.Schema, [unquote(fields), unquote(opts)])
    end
  end

  defmacro schema(name, opts, block) when is_binary(name) do
    block = block[:do]

    quote do
      @context update_context(__MODULE__, Macros.Schema, [
                 unquote(name),
                 unquote(Keyword.delete(opts, :do)),
                 unquote(Macro.escape(block))
               ])
    end
  end

  def on(:before_compile, relation, %{opts: opts}) do
    put_schema(relation, opts)

    quote location: :keep do
      @spec schema() :: Schema.t()
      def schema, do: @schema

      @spec schema(atom()) :: Field.t() | nil
      def schema(name), do: @schema[name]
    end
  end

  def put_schema(relation, opts) do
    schema =
      case context(relation, :schema) do
        %{name: nil, fields: fields} when is_list(fields) ->
          Schema.project(opts[:source].schema(), fields)

        %{name: name, infer: true, block: block} ->
          source_schema =
            case infer_source_schema(relation, name, opts) do
              nil ->
                Schema.new(%{source: String.to_atom(name)})

              schema ->
                schema
            end

          if block do
            Schema.merge(source_schema, Generator.schema_from_block(name, block))
          else
            source_schema
          end
      end

    Module.put_attribute(relation, :schema, schema)
  end

  defp infer_source_schema(relation, name, opts) do
    repo = opts[:repo]
    file = Cache.get_cache_file_path(repo, name)

    Module.put_attribute(relation, :external_resource, file)

    Cache.get_cached_schema(repo, name)
  end
end
